import { queryBillDetailPage } from "@/pages/finance/bill/services";
import { useEffect, useState } from "react";
// import logo from '@/assets/imgs/logo.svg';


const Bill = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const id = searchParams.get('id');
  const [billDetail, setBillDetail] = useState<any>({});

  useEffect(() => {
    queryBillDetailPage({ id }).then((res) => {
      setBillDetail(res);
    });
  }, []);


  return (
    <div>

    </div>
  );
};

export default Bill;